import { useToastStore, type ToastOptions } from '@/stores/toast'
import { TYPE } from 'vue-toastification'

export function useToastComposable() {
  const toastStore = useToastStore()

  return {
    // Basic toast methods
    success: (message: string, options?: ToastOptions) => toastStore.success(message, options),
    error: (message: string, options?: ToastOptions) => toastStore.error(message, options),
    warning: (message: string, options?: ToastOptions) => toastStore.warning(message, options),
    info: (message: string, options?: ToastOptions) => toastStore.info(message, options),
    custom: (message: string, type: TYPE, options?: ToastOptions) => toastStore.custom(message, type, options),
    clear: () => toastStore.clear(),

    // Convenience methods with predefined messages
    showSuccess: (message = 'Thao tác thành công!') => toastStore.success(message),
    showError: (message = 'Có lỗi xảy ra!') => toastStore.error(message),
    showWarning: (message = 'Cảnh báo!') => toastStore.warning(message),
    showInfo: (message = 'Thông tin!') => toastStore.info(message),

    // Quick methods for common scenarios
    showSaveSuccess: () => toastStore.success('Lưu thành công!'),
    showDeleteSuccess: () => toastStore.success('Xóa thành công!'),
    showUpdateSuccess: () => toastStore.success('Cập nhật thành công!'),
    showLoadError: () => toastStore.error('Không thể tải dữ liệu!'),
    showSaveError: () => toastStore.error('Lưu thất bại!'),
    showDeleteError: () => toastStore.error('Xóa thất bại!'),
    showValidationError: () => toastStore.error('Dữ liệu không hợp lệ!'),
    showNetworkError: () => toastStore.error('Lỗi kết nối mạng!'),
    
    // Loading states
    showLoading: (message = 'Đang xử lý...') => toastStore.info(message, { timeout: 0 }),
    showProcessing: (message = 'Đang xử lý, vui lòng đợi...') => toastStore.info(message, { timeout: 10000 })
  }
}
