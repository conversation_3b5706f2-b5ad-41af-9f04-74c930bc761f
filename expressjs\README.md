# ExpressJS MVC Scaffold (Sequelize + SQLite)

A full-featured Express.js MVC application.

## Features

- Express 4 with ES modules (`type: module`)
- MVC folder structure (`routes`, `controllers`, `models`, `views`)
- EJS templating
- Sequelize ORM with SQLite database
- Dotenv for environment variables
- Morgan logger & CORS
- Example home page and 404 page
- Nodemon for development reloads

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```
2. Copy `.env.example` to `.env` and adjust values.
3. Run in development:
   ```bash
   npm run dev
   ```
4. Visit `http://localhost:3000`.

## Project Structure

```
expressjs/
├── .env.example
├── package.json
├── src/
│   ├── server.js        # Entry point
│   ├── app.js           # Express config
│   ├── routes/
│   │   └── index.routes.js
│   ├── controllers/
│   │   └── home.controller.js
│   ├── models/          # (Add your Sequelize models)
│   ├── views/
│   │   ├── home.ejs
│   │   └── 404.ejs
│   └── ...
└── public/
    └── css/
        └── style.css
```

Add additional routes, controllers, and models as needed.
