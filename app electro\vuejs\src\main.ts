import './assets/main.css'
import './assets/toast.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import i18n from './i18n'
import Toast, { POSITION } from 'vue-toastification'
import 'vue-toastification/dist/index.css'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// Minimal Toast configuration - single instance only
const toastOptions = {
  position: POSITION.TOP_CENTER,
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: false,
  pauseOnHover: false,
  draggable: false,
  hideProgressBar: true,
  showCloseButtonOnHover: false,
  closeButton: false,
  icon: true,
  rtl: false,
  maxToasts: 1,
  newestOnTop: true,
  transition: "Vue-Toastification__slideBlurred"
}

app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Toast, toastOptions)

app.mount('#app')
