import './assets/main.css'
import './assets/toast.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import i18n from './i18n'
import Toast, { POSITION } from 'vue-toastification'
import 'vue-toastification/dist/index.css'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// Professional Toast configuration - optimized for smooth UX
const toastOptions = {
  // Position and layout
  position: POSITION.TOP_CENTER,
  maxToasts: 1,
  newestOnTop: true,

  // Timing and behavior
  timeout: 4000,
  closeOnClick: true,
  pauseOnFocusLoss: false, // Prevent pause conflicts
  pauseOnHover: false,     // Prevent pause conflicts

  // Visual appearance
  hideProgressBar: true,   // Cleaner look
  showCloseButtonOnHover: true,
  closeButton: false,      // Auto-close only
  icon: true,

  // Animation and transitions
  transition: "Vue-Toastification__fade",
  draggable: false,        // Disable to prevent conflicts

  // Prevent duplicate toasts and ensure smooth transitions
  filterBeforeCreate: (toast: any, toasts: any[]) => {
    // Clear existing toasts before showing new one to prevent conflicts
    if (toasts.length > 0) {
      toasts.forEach(existingToast => existingToast.remove())
    }
    return toast
  },

  // Container styling
  containerClassName: "toast-container-custom",
  toastClassName: "toast-custom"
}

app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Toast, toastOptions)

app.mount('#app')
