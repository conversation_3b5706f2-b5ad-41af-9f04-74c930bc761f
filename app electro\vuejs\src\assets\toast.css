/* Professional Toast Styles for Light and Dark Theme */

/* Custom toast container */
.toast-container-custom {
  z-index: 9999;
  top: 20px !important;
}

/* Custom toast styling */
.toast-custom {
  border-radius: 12px !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  min-height: 60px !important;
  padding: 16px 20px !important;
}

/* Smooth fade transition */
.Vue-Toastification__fade-enter-active,
.Vue-Toastification__fade-leave-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) !important;
}

.Vue-Toastification__fade-enter-from {
  opacity: 0 !important;
  transform: translateY(-20px) scale(0.95) !important;
}

.Vue-Toastification__fade-leave-to {
  opacity: 0 !important;
  transform: translateY(-10px) scale(0.98) !important;
}

/* Base toast container */
.Vue-Toastification__container {
  z-index: 9999;
}

/* Light Theme Styles */
.Vue-Toastification__toast {
  background-color: white;
  border: 1px solid #e5e7eb;
  color: #111827;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.Vue-Toastification__toast--success {
  background-color: #f0fdf4;
  border-color: #bbf7d0;
  color: #166534;
}

.Vue-Toastification__toast--error {
  background-color: #fef2f2;
  border-color: #fecaca;
  color: #991b1b;
}

.Vue-Toastification__toast--warning {
  background-color: #fffbeb;
  border-color: #fde68a;
  color: #92400e;
}

.Vue-Toastification__toast--info {
  background-color: #eff6ff;
  border-color: #bfdbfe;
  color: #1e40af;
}

/* Progress bar */
.Vue-Toastification__progress-bar {
  height: 3px;
  border-radius: 0 0 12px 12px;
}

.Vue-Toastification__progress-bar--success {
  background-color: #10b981;
}

.Vue-Toastification__progress-bar--error {
  background-color: #ef4444;
}

.Vue-Toastification__progress-bar--warning {
  background-color: #f59e0b;
}

.Vue-Toastification__progress-bar--info {
  background-color: #3b82f6;
}

/* Close button */
.Vue-Toastification__close-button {
  color: #6b7280;
  transition: color 0.2s ease;
}

.Vue-Toastification__close-button:hover {
  color: #374151;
}

/* Icons */
.Vue-Toastification__icon {
  margin-right: 12px;
}

/* Dark Theme Styles - Matching project theme */
.dark .Vue-Toastification__toast {
  background-color: #1f2937; /* bg-gray-800 */
  border-color: #374151; /* border-gray-700 */
  color: #f3f4f6; /* text-gray-100 */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.dark .Vue-Toastification__toast--success {
  background-color: #1f2937; /* Same as main bg-gray-800 */
  border-color: #10b981; /* Green accent border */
  color: #10b981; /* Green text */
}

.dark .Vue-Toastification__toast--error {
  background-color: #1f2937; /* Same as main bg-gray-800 */
  border-color: #ef4444; /* Red accent border */
  color: #ef4444; /* Red text */
}

.dark .Vue-Toastification__toast--warning {
  background-color: #1f2937; /* Same as main bg-gray-800 */
  border-color: #f59e0b; /* Yellow accent border */
  color: #f59e0b; /* Yellow text */
}

.dark .Vue-Toastification__toast--info {
  background-color: #1f2937; /* Same as main bg-gray-800 */
  border-color: #3b82f6; /* Blue accent border */
  color: #3b82f6; /* Blue text */
}

.dark .Vue-Toastification__close-button {
  color: #9ca3af; /* text-gray-400 */
}

.dark .Vue-Toastification__close-button:hover {
  color: #e5e7eb; /* text-gray-200 */
}

/* Animation enhancements */
.Vue-Toastification__toast {
  animation: slideInRight 0.3s ease-out;
}

.Vue-Toastification__toast--dismiss {
  animation: slideOutRight 0.3s ease-in;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Hover effects */
.Vue-Toastification__toast:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dark .Vue-Toastification__toast:hover {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .Vue-Toastification__container {
    padding: 16px;
  }

  .Vue-Toastification__toast {
    margin-bottom: 8px;
    border-radius: 8px;
  }
}

/* Custom toast body styling */
.Vue-Toastification__toast-body {
  padding: 16px;
  font-weight: 500;
  line-height: 1.5;
}

/* Toast body text colors for dark theme */
.dark .Vue-Toastification__toast--success .Vue-Toastification__toast-body {
  color: #d1fae5; /* Light green text */
}

.dark .Vue-Toastification__toast--error .Vue-Toastification__toast-body {
  color: #fecaca; /* Light red text */
}

.dark .Vue-Toastification__toast--warning .Vue-Toastification__toast-body {
  color: #fde68a; /* Light yellow text */
}

.dark .Vue-Toastification__toast--info .Vue-Toastification__toast-body {
  color: #bfdbfe; /* Light blue text */
}

/* Icon colors - matching project theme */
.Vue-Toastification__toast--success .Vue-Toastification__icon {
  color: #10b981;
}

.dark .Vue-Toastification__toast--success .Vue-Toastification__icon {
  color: #10b981; /* Keep consistent green */
}

/* Error icon color */
.Vue-Toastification__toast--error .Vue-Toastification__icon {
  color: #ef4444;
}

.dark .Vue-Toastification__toast--error .Vue-Toastification__icon {
  color: #ef4444; /* Keep consistent red */
}

/* Warning icon color */
.Vue-Toastification__toast--warning .Vue-Toastification__icon {
  color: #f59e0b;
}

.dark .Vue-Toastification__toast--warning .Vue-Toastification__icon {
  color: #f59e0b; /* Keep consistent yellow */
}

/* Info icon color */
.Vue-Toastification__toast--info .Vue-Toastification__icon {
  color: #3b82f6;
}

.dark .Vue-Toastification__toast--info .Vue-Toastification__icon {
  color: #3b82f6; /* Keep consistent blue */
}
