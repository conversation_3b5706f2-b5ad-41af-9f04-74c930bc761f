import './assets/main.css'
import './assets/toast.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import i18n from './i18n'
import Toast, { POSITION } from 'vue-toastification'
import 'vue-toastification/dist/index.css'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// Toast configuration
const toastOptions = {
  position: POSITION.TOP_RIGHT,
  timeout: 5000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: false,
  closeButton: "button",
  icon: true,
  rtl: false,
  transition: "Vue-Toastification__slideBlurred",
  maxToasts: 1, // Only show 1 toast at a time to prevent conflicts
  newestOnTop: true
}

app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Toast, toastOptions)

app.mount('#app')
