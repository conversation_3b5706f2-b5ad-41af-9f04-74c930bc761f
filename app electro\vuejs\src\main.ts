import './assets/main.css'
import './assets/toast.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import i18n from './i18n'
import Toast, { POSITION } from 'vue-toastification'
import 'vue-toastification/dist/index.css'

import App from './App.vue'
import router from './router'

const app = createApp(App)

// Ultra-simple Toast configuration to prevent conflicts
const toastOptions = {
  position: POSITION.TOP_CENTER,
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: false,
  pauseOnHover: false,
  draggable: false,
  hideProgressBar: true,
  showCloseButtonOnHover: false,
  closeButton: false,
  icon: true,
  rtl: false,

  // Force single toast only
  maxToasts: 1,
  newestOnTop: true,

  // Use simple slide transition only
  transition: "Vue-Toastification__slideBlurred",

  // Aggressive duplicate prevention
  filterBeforeCreate: (toast: any, toasts: any[]) => {
    // Immediately clear all existing toasts
    toasts.forEach(t => {
      if (t && typeof t.remove === 'function') {
        t.remove()
      }
    })
    return toast
  }
}

app.use(createPinia())
app.use(router)
app.use(i18n)
app.use(Toast, toastOptions)

app.mount('#app')
