<template>
  <div :class="[
    'result-box rounded-lg p-4 border mt-8',
    themeStore.isDarkMode ? 'bg-gradient-to-br from-gray-700 to-gray-600 border-gray-500 shadow-[0_4px_12px_rgba(0,0,0,0.6),inset_0_1px_0_rgba(255,255,255,0.15)]' : 'bg-indigo-50 border-indigo-100 shadow-sm'
  ]">
    <h3 :class="[
      'text-lg font-semibold mb-2',
      themeStore.isDarkMode ? 'text-white' : 'text-indigo-800'
    ]">{{ $t('calculator.summary.title') }}</h3>
    <div :class="[
      'text-2xl font-bold',
      themeStore.isDarkMode ? 'text-white' : 'text-indigo-700'
    ]">{{ formatCurrency(totalBill) }}</div>
    <p :class="[
      'text-xs mt-1',
      themeStore.isDarkMode ? 'text-gray-400' : 'text-indigo-600'
    ]">{{ $t('calculator.summary.note') }}</p>
  </div>
</template>

<script setup lang="ts">
import { useThemeStore } from '../stores/theme'

defineProps<{
  totalBill: number
  formatCurrency: (value: number) => string
}>()

const themeStore = useThemeStore()
</script>

<style scoped>
.result-box {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  animation: fadeIn 0.6s ease-out forwards;
}

.result-box:hover {
  transform: translateY(-2px);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
</style> 