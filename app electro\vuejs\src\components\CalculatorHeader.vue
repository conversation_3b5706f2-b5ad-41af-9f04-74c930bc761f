<template>
  <div :class="[
    'px-6 py-4 flex justify-between items-center',
    themeStore.isDarkMode ? 'bg-gradient-to-r from-gray-700 to-gray-600 border-b border-gray-600' : 'bg-indigo-600'
  ]">
    <div>
      <h1 class="text-2xl font-bold text-white">{{ $t('calculator.title') }}</h1>
      <p :class="themeStore.isDarkMode ? 'text-gray-200' : 'text-indigo-100'">{{ $t('calculator.subtitle') }}</p>
    </div>
    <div class="flex items-center space-x-2">
      <button 
        @click="languageStore.toggleLanguage"
        class="p-2 rounded-full hover:bg-opacity-10 hover:bg-white mr-2"
        :title="languageStore.isVietnamese ? 'Switch to English' : '<PERSON><PERSON><PERSON><PERSON> sang tiếng Việt'"
      >
        <span class="text-white font-medium">{{ languageStore.isVietnamese ? 'EN' : 'VI' }}</span>
      </button>
      <button 
        @click="themeStore.toggleTheme"
        class="p-2 rounded-full hover:bg-opacity-10 hover:bg-white"
        :title="themeStore.isDarkMode ? $t('calculator.theme.lightMode') : $t('calculator.theme.darkMode')"
      >
        <svg v-if="themeStore.isDarkMode" xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-100" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useThemeStore } from '../stores/theme'
import { useLanguageStore } from '../stores/language'

const themeStore = useThemeStore()
const languageStore = useLanguageStore()
</script> 