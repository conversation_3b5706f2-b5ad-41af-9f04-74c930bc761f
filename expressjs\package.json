{"name": "expressjs-mvc-app", "version": "1.0.0", "description": "A full-featured Express.js MVC application scaffolded by Cascade", "main": "src/server.js", "type": "module", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "migrate": "sequelize db:migrate", "migrate:undo": "sequelize db:migrate:undo:all", "lint": "eslint ."}, "keywords": ["express", "mvc", "nodejs"], "author": "", "license": "MIT", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "ejs": "^3.1.9", "express": "^4.18.2", "express-validator": "^7.0.1", "sequelize": "^6.37.1", "sqlite3": "^5.1.6", "morgan": "^1.10.0"}, "devDependencies": {"nodemon": "^3.0.1", "sequelize-cli": "^6.6.1"}}