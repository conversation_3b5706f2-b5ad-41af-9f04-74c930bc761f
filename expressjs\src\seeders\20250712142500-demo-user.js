'use strict';

module.exports = {
  async up (queryInterface, Sequelize) {
    await queryInterface.bulkInsert('Users', [
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: '<PERSON>',
        email: '<EMAIL>',
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]);
  },

  async down (queryInterface, Sequelize) {
    await queryInterface.bulkDelete('Users', null, {});
  },
};
