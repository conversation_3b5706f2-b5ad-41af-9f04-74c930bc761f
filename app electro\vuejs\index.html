<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vite App</title>
    <!-- Prevent Flash of Unstyled Content (FOUC) for dark mode -->
    <script>
      (function() {
        // Đọc theme từ localStorage
        const isDarkMode = localStorage.getItem('isDarkMode') === 'true';
        
        // Nếu người dùng đã chọn dark mode trước đó, áp dụng ngay
        if (isDarkMode) {
          // Thêm class dark-mode vào thẻ html
          document.documentElement.classList.add('dark-mode');
          
          // Thiết lập một số style mặc định cho dark mode để tránh nhấp nháy
          const style = document.createElement('style');
          style.textContent = `
            body {
              background-color: #1a202c !important;
              color: #e2e8f0 !important;
              transition: background-color 0.3s ease, color 0.3s ease;
            }
          `;
          document.head.appendChild(style);
        }
      })();
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
