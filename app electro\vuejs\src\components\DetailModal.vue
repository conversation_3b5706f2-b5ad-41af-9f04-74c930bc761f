<template>
  <div 
    v-if="showDetailModal" 
    class="fixed inset-0 flex items-center justify-center z-50 bg-black bg-opacity-50 transition-opacity duration-300 animate-fade-in"
    style="overflow-y: auto;"
  >
    <div 
      :class="[
        'relative rounded-lg max-w-lg w-full m-4 max-h-[90vh] overflow-y-auto transform transition-all duration-300 animate-scale-in',
        themeStore.isDarkMode 
          ? 'bg-gray-800 border border-gray-700 text-white shadow-[0_25px_50px_rgba(0,0,0,0.5)]' 
          : 'bg-white text-gray-800 shadow-2xl'
      ]"
    >
      <div class="sticky top-0 z-10 flex justify-between items-center p-4 border-b"
        :class="themeStore.isDarkMode ? 'bg-gradient-to-r from-gray-700 to-gray-600 border-gray-600' : 'bg-white border-gray-200'"
      >
        <h3 class="text-lg font-medium">{{ $t('calculator.detailModal.title') }}</h3>
        <button 
          @click="closeDetailModal"
          :class="[
            'p-2 rounded-full transition-all duration-200 focus:outline-none',
            themeStore.isDarkMode 
              ? 'hover:bg-gray-600 hover:text-white active:bg-gray-500 hover:shadow-md'
              : 'hover:bg-red-100 text-gray-700 hover:text-red-600 active:bg-red-200'
          ]"
          :title="$t('calculator.detailModal.closeButton')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      
      <div class="p-6 pt-4 space-y-6 text-left">
        <!-- Ngày tháng -->
        <div class="text-center text-lg font-medium">
          {{ currentDate }}
        </div>
        
        <!-- Phần điện -->
        <div>
          <div class="font-medium text-lg mb-2">+ {{ $t('calculator.tabs.electricity') }}:</div>
          <div :class="[
            'rounded-lg p-4',
            themeStore.isDarkMode ? 'bg-gray-900 border border-gray-600 shadow-[0_2px_4px_rgba(0,0,0,0.3)]' : 'bg-gray-50'
          ]">
            <div class="flex items-start mb-2">
              <span class="w-20">{{ $t('calculator.detailModal.reading') }}:</span>
              <span class="font-medium">
                {{ $t('calculator.detailModal.new') }}: {{ electricityNew || '0' }} - {{ $t('calculator.detailModal.old') }}: {{ electricityOld || '0' }} = {{ electricityUsage }} kWh
              </span>
            </div>
            <div class="flex items-start mb-2">
              <span class="w-20">{{ $t('calculator.detailModal.amount') }}:</span>
              <span class="font-medium">{{ electricityRate }} × {{ electricityUsage }} = {{ formatCurrency(electricityTotal) }}</span>
            </div>
          </div>
        </div>
        
        <!-- Phần nước -->
        <div>
          <div class="font-medium text-lg mb-2">+ {{ $t('calculator.tabs.water') }}:</div>
          <div :class="[
            'rounded-lg p-4',
            themeStore.isDarkMode ? 'bg-gray-900 border border-gray-600 shadow-[0_2px_4px_rgba(0,0,0,0.3)]' : 'bg-gray-50'
          ]">
            <div class="flex items-start mb-2">
              <span class="w-20">{{ $t('calculator.detailModal.reading') }}:</span>
              <span class="font-medium">
                {{ $t('calculator.detailModal.new') }}: {{ waterNew || '0' }} - {{ $t('calculator.detailModal.old') }}: {{ waterOld || '0' }} = {{ waterUsage }} m³
              </span>
            </div>
            <div class="flex items-start mb-2">
              <span class="w-20">{{ $t('calculator.detailModal.amount') }}:</span>
              <span class="font-medium">{{ waterRate }} × {{ waterUsage }} = {{ formatCurrency(waterTotal) }}</span>
            </div>
          </div>
        </div>
        
        <!-- Tổng điện nước -->
        <div>
          <div class="font-medium text-lg mb-2">{{ $t('calculator.detailModal.utilityTotal') }}:</div>
          <div :class="[
            'rounded-lg p-4',
            themeStore.isDarkMode ? 'bg-gray-900 border border-gray-600 shadow-[0_2px_4px_rgba(0,0,0,0.3)]' : 'bg-gray-50'
          ]">
            <div class="font-medium">
              {{ formatCurrency(electricityTotal) }} + {{ formatCurrency(waterTotal) }} = {{ formatCurrency(totalBill) }}
            </div>
          </div>
        </div>
        
        <!-- Phần tổng cộng -->
        <div>
          <div class="font-medium text-lg mb-2">{{ $t('calculator.detailModal.grandTotal') }}:</div>
          <div :class="[
            'rounded-lg p-4 border-2 text-center',
            themeStore.isDarkMode ? 'bg-gradient-to-br from-gray-700 to-gray-600 border-gray-500 shadow-[0_4px_12px_rgba(0,0,0,0.6),inset_0_1px_0_rgba(255,255,255,0.15)]' : 'bg-blue-50 border-blue-200'
          ]">
            <div class="text-xl font-bold">
              {{ formatCurrency(totalBill) }}
            </div>
            <div class="text-sm mt-1" :class="themeStore.isDarkMode ? 'text-gray-300' : 'text-gray-600'">
              {{ $t('calculator.detailModal.note') }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- Thanh chỉ dẫn cách đóng modal -->
      <div 
        :class="[
          'py-2 text-center text-xs sticky bottom-0',
          themeStore.isDarkMode ? 'bg-gray-700 text-gray-400 border-t border-gray-600' : 'bg-gray-50 text-gray-500 border-t border-gray-200'
        ]"
      >
        <span class="flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {{ $t('calculator.detailModal.closeButton') }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useThemeStore } from '../stores/theme'

defineProps<{
  showDetailModal: boolean
  currentDate: string
  electricityOld: number | string
  electricityNew: number | string
  electricityRate: number
  electricityUsage: number
  electricityTotal: number
  waterOld: number | string
  waterNew: number | string
  waterRate: number
  waterUsage: number
  waterTotal: number
  totalBill: number
  formatCurrency: (value: number) => string
}>()

const emit = defineEmits(['close-detail-modal'])

const themeStore = useThemeStore()

const closeDetailModal = () => {
  emit('close-detail-modal')
}
</script>

<style scoped>
/* Modal animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes scaleIn {
  from { transform: scale(0.95); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}
</style> 