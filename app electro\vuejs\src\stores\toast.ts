import { defineStore } from 'pinia'
import { ref } from 'vue'
import { useToast, POSITION, TYPE } from 'vue-toastification'

export interface ToastOptions {
  position?: POSITION
  timeout?: number
  closeOnClick?: boolean
  pauseOnFocusLoss?: boolean
  pauseOnHover?: boolean
  draggable?: boolean
  draggablePercent?: number
  showCloseButtonOnHover?: boolean
  hideProgressBar?: boolean
  closeButton?: boolean | string
  icon?: boolean | string
  rtl?: boolean
}

export const useToastStore = defineStore('toast', () => {
  const toast = useToast()
  
  // Simple default options - consistent with main.ts
  const defaultOptions: ToastOptions = {
    position: POSITION.TOP_CENTER,
    timeout: 3000,
    closeOnClick: true,
    pauseOnFocusLoss: false,
    pauseOnHover: false,
    draggable: false,
    showCloseButtonOnHover: false,
    hideProgressBar: true,
    closeButton: false,
    icon: true,
    rtl: false
  }

  // Toast methods
  function success(message: string, options?: ToastOptions) {
    toast.success(message, { ...defaultOptions, ...options })
  }

  function error(message: string, options?: ToastOptions) {
    toast.error(message, { ...defaultOptions, ...options })
  }

  function warning(message: string, options?: ToastOptions) {
    toast.warning(message, { ...defaultOptions, ...options })
  }

  function info(message: string, options?: ToastOptions) {
    toast.info(message, { ...defaultOptions, ...options })
  }

  function custom(message: string, type: TYPE, options?: ToastOptions) {
    toast(message, { ...defaultOptions, ...options, type })
  }

  function clear() {
    toast.clear()
  }

  return {
    success,
    error,
    warning,
    info,
    custom,
    clear,
    defaultOptions
  }
})
